"""
反馈处理器

负责收集、处理和分析用户反馈，用于改进系统性能
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4
from datetime import datetime
from enum import Enum

from src.common.logger import get_logger

logger = get_logger(__name__)


class FeedbackType(str, Enum):
    """反馈类型"""
    SCRIPT_CORRECTION = "script_correction"
    TEST_CASE_IMPROVEMENT = "test_case_improvement"
    PERFORMANCE_ISSUE = "performance_issue"
    BUG_REPORT = "bug_report"
    FEATURE_REQUEST = "feature_request"
    GENERAL = "general"


class FeedbackSeverity(str, Enum):
    """反馈严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class FeedbackProcessor:
    """
    反馈处理器
    
    负责收集、处理和分析用户反馈
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._initialized = False
        
        # 反馈存储
        self._feedback_storage: Dict[UUID, Dict[str, Any]] = {}
        self._feedback_queue: asyncio.Queue = asyncio.Queue()
        
        # 处理统计
        self._total_feedback = 0
        self._processed_feedback = 0
        self._pending_feedback = 0
        
        # 知识库更新队列
        self._knowledge_updates: List[Dict[str, Any]] = []
        
    async def initialize(self) -> None:
        """初始化反馈处理器"""
        try:
            self.logger.info("Initializing FeedbackProcessor")
            
            # 启动反馈处理循环
            await self._start_processing_loop()
            
            # 初始化知识库连接
            await self._initialize_knowledge_base()
            
            self._initialized = True
            
            self.logger.info("FeedbackProcessor initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize FeedbackProcessor: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up FeedbackProcessor")
            
            # 处理剩余的反馈
            await self._process_remaining_feedback()
            
            # 清理存储
            self._feedback_storage.clear()
            self._knowledge_updates.clear()
            
            self._initialized = False
            
            self.logger.info("FeedbackProcessor cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during FeedbackProcessor cleanup: {e}")
    
    async def submit_feedback(
        self,
        session_id: UUID,
        feedback_type: FeedbackType,
        content: str,
        severity: FeedbackSeverity = FeedbackSeverity.MEDIUM,
        metadata: Optional[Dict[str, Any]] = None
    ) -> UUID:
        """
        提交反馈
        
        Args:
            session_id: 会话ID
            feedback_type: 反馈类型
            content: 反馈内容
            severity: 严重程度
            metadata: 元数据
            
        Returns:
            UUID: 反馈ID
        """
        if not self._initialized:
            raise RuntimeError("FeedbackProcessor not initialized")
        
        try:
            feedback_id = uuid4()
            
            feedback_data = {
                "feedback_id": feedback_id,
                "session_id": session_id,
                "type": feedback_type,
                "content": content,
                "severity": severity,
                "metadata": metadata or {},
                "submitted_at": datetime.now(),
                "status": "pending",
                "processed_at": None
            }
            
            # 存储反馈
            self._feedback_storage[feedback_id] = feedback_data
            
            # 添加到处理队列
            await self._feedback_queue.put(feedback_data)
            
            self._total_feedback += 1
            self._pending_feedback += 1
            
            self.logger.info(f"Feedback {feedback_id} submitted successfully")
            
            return feedback_id
            
        except Exception as e:
            self.logger.error(f"Failed to submit feedback: {e}")
            raise
    
    async def get_feedback_status(self, feedback_id: UUID) -> Optional[Dict[str, Any]]:
        """获取反馈状态"""
        try:
            feedback_data = self._feedback_storage.get(feedback_id)
            
            if feedback_data:
                return {
                    "feedback_id": feedback_id,
                    "status": feedback_data["status"],
                    "submitted_at": feedback_data["submitted_at"],
                    "processed_at": feedback_data["processed_at"],
                    "type": feedback_data["type"],
                    "severity": feedback_data["severity"]
                }
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to get feedback status for {feedback_id}: {e}")
            return None
    
    async def process_feedback(self, feedback_data: Dict[str, Any]) -> bool:
        """
        处理单个反馈
        
        Args:
            feedback_data: 反馈数据
            
        Returns:
            bool: 是否处理成功
        """
        try:
            feedback_id = feedback_data["feedback_id"]
            feedback_type = feedback_data["type"]
            content = feedback_data["content"]
            
            self.logger.info(f"Processing feedback {feedback_id} of type {feedback_type}")
            
            # 根据反馈类型进行不同的处理
            if feedback_type == FeedbackType.SCRIPT_CORRECTION:
                await self._process_script_correction(feedback_data)
            elif feedback_type == FeedbackType.TEST_CASE_IMPROVEMENT:
                await self._process_test_case_improvement(feedback_data)
            elif feedback_type == FeedbackType.PERFORMANCE_ISSUE:
                await self._process_performance_issue(feedback_data)
            elif feedback_type == FeedbackType.BUG_REPORT:
                await self._process_bug_report(feedback_data)
            elif feedback_type == FeedbackType.FEATURE_REQUEST:
                await self._process_feature_request(feedback_data)
            else:
                await self._process_general_feedback(feedback_data)
            
            # 更新状态
            feedback_data["status"] = "processed"
            feedback_data["processed_at"] = datetime.now()
            
            self._processed_feedback += 1
            self._pending_feedback -= 1
            
            self.logger.info(f"Feedback {feedback_id} processed successfully")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to process feedback: {e}")
            
            # 更新状态为失败
            feedback_data["status"] = "failed"
            feedback_data["processed_at"] = datetime.now()
            feedback_data["error"] = str(e)
            
            return False
    
    async def _start_processing_loop(self) -> None:
        """启动反馈处理循环"""
        try:
            # 启动后台任务处理反馈队列
            asyncio.create_task(self._process_feedback_queue())
            
            self.logger.info("Feedback processing loop started")
            
        except Exception as e:
            self.logger.error(f"Failed to start processing loop: {e}")
            raise
    
    async def _process_feedback_queue(self) -> None:
        """处理反馈队列"""
        while self._initialized:
            try:
                # 从队列获取反馈
                feedback_data = await asyncio.wait_for(
                    self._feedback_queue.get(), timeout=1.0
                )
                
                # 处理反馈
                await self.process_feedback(feedback_data)
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                self.logger.error(f"Error processing feedback queue: {e}")
                await asyncio.sleep(1)
    
    async def _initialize_knowledge_base(self) -> None:
        """初始化知识库连接"""
        try:
            # 这里应该初始化与知识库的连接
            # 暂时使用模拟实现
            
            self.logger.info("Knowledge base connection initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize knowledge base: {e}")
            raise
    
    async def _process_script_correction(self, feedback_data: Dict[str, Any]) -> None:
        """处理脚本修正反馈"""
        try:
            # 分析脚本修正内容
            content = feedback_data["content"]
            metadata = feedback_data.get("metadata", {})
            
            # 提取改进模式
            improvement_pattern = self._extract_improvement_pattern(content)
            
            # 更新知识库
            knowledge_update = {
                "type": "script_improvement",
                "pattern": improvement_pattern,
                "original_content": metadata.get("original_script"),
                "corrected_content": content,
                "timestamp": datetime.now()
            }
            
            self._knowledge_updates.append(knowledge_update)
            
            self.logger.info("Script correction feedback processed")
            
        except Exception as e:
            self.logger.error(f"Failed to process script correction: {e}")
            raise
    
    async def _process_test_case_improvement(self, feedback_data: Dict[str, Any]) -> None:
        """处理测试用例改进反馈"""
        try:
            # 处理测试用例改进建议
            content = feedback_data["content"]
            
            # 分析改进建议
            improvement_suggestions = self._analyze_test_case_improvements(content)
            
            # 更新知识库
            knowledge_update = {
                "type": "test_case_improvement",
                "suggestions": improvement_suggestions,
                "feedback_content": content,
                "timestamp": datetime.now()
            }
            
            self._knowledge_updates.append(knowledge_update)
            
            self.logger.info("Test case improvement feedback processed")
            
        except Exception as e:
            self.logger.error(f"Failed to process test case improvement: {e}")
            raise
    
    async def _process_performance_issue(self, feedback_data: Dict[str, Any]) -> None:
        """处理性能问题反馈"""
        try:
            # 记录性能问题
            content = feedback_data["content"]
            severity = feedback_data["severity"]
            
            # 如果是高严重程度，立即记录
            if severity in [FeedbackSeverity.HIGH, FeedbackSeverity.CRITICAL]:
                self.logger.warning(f"High severity performance issue reported: {content}")
            
            self.logger.info("Performance issue feedback processed")
            
        except Exception as e:
            self.logger.error(f"Failed to process performance issue: {e}")
            raise
    
    async def _process_bug_report(self, feedback_data: Dict[str, Any]) -> None:
        """处理错误报告反馈"""
        try:
            # 记录错误报告
            content = feedback_data["content"]
            metadata = feedback_data.get("metadata", {})
            
            # 提取错误信息
            error_info = {
                "description": content,
                "stack_trace": metadata.get("stack_trace"),
                "environment": metadata.get("environment"),
                "timestamp": datetime.now()
            }
            
            self.logger.error(f"Bug report received: {json.dumps(error_info, default=str)}")
            
        except Exception as e:
            self.logger.error(f"Failed to process bug report: {e}")
            raise
    
    async def _process_feature_request(self, feedback_data: Dict[str, Any]) -> None:
        """处理功能请求反馈"""
        try:
            # 记录功能请求
            content = feedback_data["content"]
            
            self.logger.info(f"Feature request received: {content}")
            
        except Exception as e:
            self.logger.error(f"Failed to process feature request: {e}")
            raise
    
    async def _process_general_feedback(self, feedback_data: Dict[str, Any]) -> None:
        """处理一般反馈"""
        try:
            # 记录一般反馈
            content = feedback_data["content"]
            
            self.logger.info(f"General feedback received: {content}")
            
        except Exception as e:
            self.logger.error(f"Failed to process general feedback: {e}")
            raise
    
    def _extract_improvement_pattern(self, content: str) -> Dict[str, Any]:
        """提取改进模式"""
        # 简单的模式提取实现
        return {
            "content_length": len(content),
            "contains_code": "def " in content or "class " in content,
            "contains_assertion": "assert" in content,
            "improvement_type": "general"
        }
    
    def _analyze_test_case_improvements(self, content: str) -> List[str]:
        """分析测试用例改进建议"""
        # 简单的改进建议分析
        suggestions = []
        
        if "边界" in content or "boundary" in content.lower():
            suggestions.append("增加边界条件测试")
        
        if "异常" in content or "exception" in content.lower():
            suggestions.append("增加异常处理测试")
        
        if "性能" in content or "performance" in content.lower():
            suggestions.append("增加性能测试")
        
        return suggestions if suggestions else ["一般改进建议"]
    
    async def _process_remaining_feedback(self) -> None:
        """处理剩余的反馈"""
        while not self._feedback_queue.empty():
            try:
                feedback_data = await asyncio.wait_for(
                    self._feedback_queue.get(), timeout=0.1
                )
                await self.process_feedback(feedback_data)
            except asyncio.TimeoutError:
                break
            except Exception as e:
                self.logger.error(f"Error processing remaining feedback: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_feedback": self._total_feedback,
            "processed_feedback": self._processed_feedback,
            "pending_feedback": self._pending_feedback,
            "knowledge_updates": len(self._knowledge_updates)
        }
