"""
Orchestrator 服务类

实现核心编排逻辑，协调各个模块完成测试用例生成、脚本生成和执行管理
"""

import asyncio
from typing import Dict, List, Optional, AsyncGenerator, Any
from uuid import UUID, uuid4
from datetime import datetime
import threading

from src.common.config import settings
from src.common.logger import get_logger
from src.orchestrator.models import (
    SessionContext,
    TestCaseGenerationRequest,
    TestCaseGenerationResponse,
    ScriptGenerationRequest,
    ScriptGenerationResponse,
    ExecutionRequest,
    ExecutionResponse,
    ExecutionStatus,
    ExecutionState,
    TaskStatus,
    TestCaseDefinition,
    ScriptDefinition,
)
from src.test_case.generator import TestCaseGenerator
from src.ai.llm_client import LLMProvider

logger = get_logger(__name__)


class OrchestratorService:
    """
    Orchestrator 服务类
    
    负责协调各个模块，管理会话上下文，处理业务流程
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        # TODO: 实现专用的AI和Crypto日志适配器
        self.ai_logger = self.logger
        self.crypto_logger = self.logger

        # 初始化测试用例生成器
        self._test_case_generator = TestCaseGenerator()

        # 会话存储 (生产环境应使用Redis或数据库)
        self._sessions: Dict[UUID, SessionContext] = {}

        # 任务存储 (生产环境应使用Redis或数据库)
        self._tasks: Dict[UUID, Dict[str, Any]] = {}

        # 执行状态存储 (生产环境应使用Redis或数据库)
        self._executions: Dict[UUID, ExecutionStatus] = {}

        # 后台任务跟踪 - 防止内存泄漏
        self._background_tasks: Dict[UUID, asyncio.Task] = {}

        # 线程安全锁
        self._session_lock = asyncio.Lock()
        self._execution_lock = asyncio.Lock()

        # 服务状态
        self._initialized = False
        self._ready = False
        
    async def initialize(self) -> None:
        """
        初始化服务
        """
        try:
            self.logger.info("Initializing Orchestrator service")
            
            # 初始化各个模块连接
            await self._initialize_modules()
            
            # 初始化数据存储
            await self._initialize_storage()
            
            # 验证配置
            await self._validate_configuration()
            
            # 加载插件 (设计文档3.1要求)
            await self._load_plugins()
            
            self._initialized = True
            self._ready = True
            
            self.logger.info("Orchestrator service initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Orchestrator service: {e}")
            raise
            
    async def _load_plugins(self):
        """加载插件 (设计文档3.1要求)"""
        try:
            if settings.environment == "test":
                self.logger.info("Skipping plugin loading in test mode")
                return
                
            from src.common.plugin_manager import PluginManager
            plugin_dir = getattr(settings, 'plugin_dir', 'src/plugins')
            self.plugin_manager = PluginManager(plugin_dir)
            await self.plugin_manager.load_plugins()
            self.logger.info(
                f"Plugins loaded: {list(self.plugin_manager.plugins.keys())}"
            )
        except Exception as e:
            self.logger.error(f"Failed to load plugins: {e}")
            raise
    
    async def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            self.logger.info("Cleaning up Orchestrator service")
            
            # 停止所有运行中的任务
            await self._stop_running_tasks()
            
            # 清理连接
            await self._cleanup_connections()
            
            # 卸载插件 (设计文档3.1要求)
            if hasattr(self, 'plugin_manager'):
                await self.plugin_manager.unload_plugins()
                self.logger.info("Plugins unloaded successfully")
            
            # 清理加密客户端
            if hasattr(self, 'crypto_client'):
                await self.crypto_client.cleanup()
                self.logger.info("Crypto client cleaned up")
            
            self._ready = False
            self._initialized = False
            
            self.logger.info("Orchestrator service cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def is_ready(self) -> bool:
        """
        检查服务是否就绪
        
        Returns:
            bool: 服务就绪状态
        """
        return self._ready
    
    async def create_session(self) -> SessionContext:
        """
        创建新的会话上下文
        
        Returns:
            SessionContext: 新创建的会话
        """
        session = SessionContext()

        # 线程安全的会话创建
        async with self._session_lock:
            self._sessions[session.session_id] = session

        self.logger.info(
            f"Session created: {session.session_id} at {session.created_at.isoformat()}"
        )

        return session
    
    async def get_session(self, session_id: UUID) -> Optional[SessionContext]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[SessionContext]: 会话信息，如果不存在则返回None
        """
        return self._sessions.get(session_id)
    
    async def generate_test_cases(
        self, request: TestCaseGenerationRequest
    ) -> TestCaseGenerationResponse:
        """
        生成测试用例
        
        Args:
            request: 测试用例生成请求
            
        Returns:
            TestCaseGenerationResponse: 测试用例生成响应
        """
        task_id = uuid4()
        
        self.ai_logger.info(
            f"Starting AI operation: test_case_generation for session {request.session_id}, task {task_id}"
        )
        
        try:
            # 更新会话上下文 (线程安全)
            async with self._session_lock:
                session = await self.get_session(request.session_id)
                if not session:
                    raise ValueError(f"Session {request.session_id} not found")

                session.updated_at = datetime.now()
            
            # 调用测试用例生成模块 (暂时使用模拟数据)
            test_cases = await self._generate_test_cases_impl(request)
            
            response = TestCaseGenerationResponse(
                task_id=task_id,
                session_id=request.session_id,
                status=TaskStatus.COMPLETED,
                test_cases=test_cases,
                metadata={
                    "requirement_length": len(request.requirement_text),
                    "crypto_enabled": request.crypto_enabled,
                    "generation_time": datetime.now().isoformat()
                }
            )
            
            # 存储任务结果
            self._tasks[task_id] = {
                "type": "test_case_generation",
                "request": request.model_dump(),
                "response": response.model_dump(),
                "created_at": datetime.now()
            }
            
            self.ai_logger.info(
                f"AI operation completed: test_case_generation for session {request.session_id}, task {task_id}, generated {len(test_cases)} test cases"
            )
            
            return response
            
        except Exception as e:
            self.logger.error(
                f"Test case generation failed for session {request.session_id}, task {task_id}: {e}"
            )
            
            self.ai_logger.error(
                f"AI operation failed: test_case_generation for session {request.session_id}, task {task_id}: {e}"
            )
            
            raise
    
    async def generate_scripts(
        self, request: ScriptGenerationRequest
    ) -> ScriptGenerationResponse:
        """
        生成测试脚本
        
        Args:
            request: 脚本生成请求
            
        Returns:
            ScriptGenerationResponse: 脚本生成响应
        """
        task_id = uuid4()
        
        self.ai_logger.info(
            f"Starting AI operation: script_generation for session {request.session_id}, task {task_id}"
        )
        
        try:
            # 更新会话上下文
            session = await self.get_session(request.session_id)
            if not session:
                raise ValueError(f"Session {request.session_id} not found")
            
            session.updated_at = datetime.now()
            
            # 调用脚本生成模块 (暂时使用模拟数据)
            scripts = await self._generate_scripts_impl(request)
            
            response = ScriptGenerationResponse(
                task_id=task_id,
                session_id=request.session_id,
                status=TaskStatus.COMPLETED,
                scripts=scripts,
                metadata={
                    "test_case_count": len(request.test_case_ids),
                    "target_language": request.target_language,
                    "target_framework": request.target_framework,
                    "generation_time": datetime.now().isoformat()
                }
            )
            
            # 存储任务结果
            self._tasks[task_id] = {
                "type": "script_generation",
                "request": request.model_dump(),
                "response": response.model_dump(),
                "created_at": datetime.now()
            }
            
            return response
            
        except Exception as e:
            self.logger.error(
                f"Script generation failed for session {request.session_id}, task {task_id}: {e}"
            )
            raise
    
    async def start_execution(
        self, request: ExecutionRequest
    ) -> ExecutionResponse:
        """
        启动测试执行
        
        Args:
            request: 执行请求
            
        Returns:
            ExecutionResponse: 执行响应
        """
        execution_id = uuid4()
        
        try:
            # 创建执行状态
            execution_status = ExecutionStatus(
                execution_id=execution_id,
                session_id=request.session_id,
                status=ExecutionState.QUEUED,
                total_scripts=len(request.script_ids),
                start_time=datetime.now()
            )
            
            self._executions[execution_id] = execution_status

            # 启动后台执行任务并跟踪
            task = asyncio.create_task(self._execute_scripts_background(execution_id, request))
            self._background_tasks[execution_id] = task

            # 添加任务完成回调以清理
            task.add_done_callback(lambda t: self._cleanup_background_task(execution_id, t))
            
            response = ExecutionResponse(
                execution_id=execution_id,
                session_id=request.session_id,
                status=ExecutionState.QUEUED,
                script_count=len(request.script_ids)
            )
            
            self.logger.info(
                f"Test execution started: {execution_id} for session {request.session_id}, {len(request.script_ids)} scripts"
            )
            
            return response
            
        except Exception as e:
            self.logger.error(
                f"Failed to start execution {execution_id} for session {request.session_id}: {e}"
            )
            raise
    
    async def get_execution_status(self, execution_id: UUID) -> Optional[ExecutionStatus]:
        """
        获取执行状态
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[ExecutionStatus]: 执行状态，如果不存在则返回None
        """
        return self._executions.get(execution_id)
    
    async def get_execution_logs(self, execution_id: UUID) -> Optional[AsyncGenerator[str, None]]:
        """
        获取执行日志流
        
        Args:
            execution_id: 执行ID
            
        Returns:
            Optional[AsyncGenerator[str, None]]: 日志流，如果不存在则返回None
        """
        execution = self._executions.get(execution_id)
        if not execution:
            return None
        
        # 模拟日志流
        async def log_generator():
            for i in range(10):
                yield f"Log line {i} for execution {execution_id}\n"
                await asyncio.sleep(0.1)
        
        return log_generator()
    
    async def process_feedback(self, feedback_data: Dict[str, Any]) -> None:
        """
        处理反馈数据 (设计文档3.7要求)
        
        Args:
            feedback_data: 反馈数据
        """
        try:
            self.logger.info(
                "Processing feedback",
                session_id=feedback_data.get('session_id'),
                feedback_type=feedback_data.get('type')
            )
            
            # 调用反馈模块处理
            await self.feedback_processor.process(feedback_data)
            
            # 更新知识库
            await self._update_knowledge_base_from_feedback(feedback_data)
            
            self.logger.info("Feedback processed successfully")
            
        except Exception as e:
            self.logger.error(f"Feedback processing failed: {e}")
            raise
        
    async def _update_knowledge_base_from_feedback(self, feedback_data: Dict) -> None:
        """使用反馈更新知识库 (设计文档3.7)"""
        # 调用知识库API或本地服务
        # TODO: 替换为真实集成
        self.logger.info(
            f"Updating knowledge base from feedback type {feedback_data.get('type')}"
        )
    
    # 私有方法
    
    async def _initialize_modules(self) -> None:
        """初始化各个模块 (设计文档3.1要求)"""
        try:
            self.logger.info("Initializing modules")
            
            # 初始化测试用例生成器，启用AI功能
            await self._test_case_generator.initialize(
                enable_ai=True,
                ai_provider=LLMProvider.MOCK  # 使用Mock AI用于测试
            )
            
            # 初始化脚本生成模块 (设计文档3.4要求)
            from src.script_gen.template_engine import TemplateEngine
            self.script_engine = TemplateEngine()
            await self.script_engine.initialize()
            
            # 初始化执行调度模块 (设计文档3.5要求)
            from src.scheduler.scheduler import ExecutionScheduler
            self.scheduler = ExecutionScheduler(model_loader=self._get_model_loader)
            await self.scheduler.initialize()
            
            # 初始化解密客户端 (设计文档3.2要求)
            from src.crypto.client import CryptoClient
            self.crypto_client = CryptoClient()
            await self.crypto_client.initialize()
            
            # 初始化反馈模块 (设计文档3.7要求)
            from src.feedback.processor import FeedbackProcessor
            self.feedback_processor = FeedbackProcessor()
            await self.feedback_processor.initialize()
            
            # 初始化分析模块 (设计文档3.6要求)
            from src.analysis.analyzer import ExecutionAnalyzer
            self.execution_analyzer = ExecutionAnalyzer()
            await self.execution_analyzer.initialize()
            
            self.logger.info("All modules initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize modules: {e}")
            raise
    
    async def _initialize_storage(self) -> None:
        """初始化数据存储"""
        # 初始化Redis连接
        # 初始化数据库连接
        pass
    
    async def _validate_configuration(self) -> None:
        """验证配置"""
        # 验证AI模型配置
        # 验证Crypto配置
        # 验证数据库配置
        pass
    
    async def _stop_running_tasks(self) -> None:
        """停止所有运行中的任务"""
        # 取消所有后台任务
        for execution_id, task in self._background_tasks.items():
            if not task.done():
                self.logger.info(f"Cancelling background task for execution {execution_id}")
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self._background_tasks.clear()

    def _cleanup_background_task(self, execution_id: UUID, task: asyncio.Task) -> None:
        """清理完成的后台任务"""
        try:
            # 移除已完成的任务
            self._background_tasks.pop(execution_id, None)

            # 检查任务是否有异常
            if task.exception():
                self.logger.error(
                    f"Background task for execution {execution_id} failed: {task.exception()}"
                )
        except Exception as e:
            self.logger.error(f"Error cleaning up background task {execution_id}: {e}")
    
    async def _cleanup_connections(self) -> None:
        """清理连接"""
        # 清理数据库连接
        # 清理Redis连接
        pass
    
    async def _generate_test_cases_impl(
        self, request: TestCaseGenerationRequest
    ) -> List[TestCaseDefinition]:
        """
        实际的测试用例生成实现
        
        Args:
            request: 测试用例生成请求
            
        Returns:
            List[TestCaseDefinition]: 生成的测试用例列表
        """
        try:
            # 使用TestCaseGenerator生成测试用例
            from src.test_case.models import GenerationContext, GenerationOptions
            
            # 构建生成上下文
            context = GenerationContext(
                session_id=request.session_id,
                domain=request.context.get("domain"),
                system_type=request.context.get("system_type"),
                technology_stack=request.context.get("technology_stack", []),
                security_requirements=request.context.get("security_requirements", {}),
                crypto_enabled=request.crypto_enabled
            )
            
            # 构建生成选项
            options = GenerationOptions(
                target_coverage=request.target_coverage,
                max_test_cases=request.generation_options.get("max_test_cases", 20),
                include_boundary_tests=request.generation_options.get("include_boundary_tests", True),
                include_exception_tests=request.generation_options.get("include_exception_tests", True),
                include_security_tests=request.generation_options.get("include_security_tests", False),
                crypto_enabled=request.crypto_enabled,
                signature_enabled=request.crypto_enabled,
                risk_assessment_enabled=True,
                use_templates=True,
                generate_test_data=True,
            )
            
            # 调用生成器 - 修正参数传递
            generated_cases = await self._test_case_generator.generate_test_cases(
                requirement_text=request.requirement_text,
                context=context,
                options=options
            )
            
            # 转换为Orchestrator模型格式
            test_cases = []
            for case in generated_cases:
                orchestrator_case = TestCaseDefinition(
                    test_case_id=case.id,
                    title=case.title,
                    description=case.description,
                    scenario=case.scenario or case.description,
                    preconditions=case.preconditions,
                    test_steps=case.test_steps or case.steps,
                    expected_results=case.expected_results or [case.expected_result] if case.expected_result else [],
                    test_data=case.test_data,
                    priority=case.priority,
                    tags=case.tags,
                    risk_level=case.risk_level,
                    created_at=case.created_at
                )
                test_cases.append(orchestrator_case)
            
            return test_cases
            
        except Exception as e:
            self.logger.error(f"Test case generation implementation failed: {e}")
            # 返回模拟数据作为后备
            return [
                TestCaseDefinition(
                    title="示例测试用例",
                    description="这是一个示例测试用例",
                    scenario="基本功能测试场景",
                    preconditions=["系统正常运行"],
                    test_steps=["执行基本操作", "验证结果"],
                    expected_results=["操作成功", "结果正确"],
                    test_data={"input": "test_data", "expected": "success"},
                    priority="medium",
                    tags=["example", "basic"],
                    risk_level="low"
                )
            ]
    
    async def _generate_scripts_impl(
        self, request: ScriptGenerationRequest
    ) -> List[ScriptDefinition]:
        """
        脚本生成实现 (设计文档3.4要求)
        
        Args:
            request: 脚本生成请求
            
        Returns:
            List[ScriptDefinition]: 生成的脚本列表
        """
        try:
            # 调用实际的脚本生成模块
            generated_scripts = await self.script_engine.generate_scripts(
                test_case_ids=request.test_case_ids,
                target_language=request.target_language,
                target_framework=request.target_framework
            )
            
            # 添加加密相关逻辑 (设计文档3.4安全集成)
            if request.crypto_config:
                for script in generated_scripts:
                    # 添加加密签名逻辑到脚本中
                    script.content = self.crypto_client.add_crypto_flows(script.content, request.crypto_config)
              
            return generated_scripts
        except Exception as e:
            self.logger.error(f"Script generation failed: {e}")
            raise
    
    async def _execute_scripts_background(
        self, execution_id: UUID, request: ExecutionRequest
    ) -> None:
        """
        后台执行脚本 (设计文档3.5要求调用调度器)
        
        Args:
            execution_id: 执行ID
            request: 执行请求
        """
        execution = self._executions.get(execution_id)
        if not execution:
            return
        
        try:
            execution.status = ExecutionState.RUNNING
            
            # 真实调用执行调度模块
            await self.scheduler.schedule_execution(
                script_ids=request.script_ids,
                environment=request.environment
            )
            
            # 监控执行状态
            while not self.scheduler.execution_completed(execution_id):
                await asyncio.sleep(1)
                status = self.scheduler.get_execution_status(execution_id)
                execution.completed_scripts = status.completed_scripts
                execution.failed_scripts = status.failed_scripts
                execution.progress = status.progress
            
            # 分析执行结果 (设计文档3.6要求)
            report = await self.execution_analyzer.analyze_execution(execution_id)
            execution.analysis_report = report
            
            execution.status = ExecutionState.COMPLETED
            execution.end_time = datetime.now()
            
            self.logger.info(
                f"Test execution completed: {execution_id} in {(execution.end_time - execution.start_time).total_seconds():.2f}s"
            )
            
        except Exception as e:
            execution.status = ExecutionState.FAILED
            execution.error_message = str(e)
            execution.end_time = datetime.now()
            
            self.logger.error(
                f"Test execution failed: {execution_id} - {e}"
            )
            # 发送失败通知 (设计文档3.6要求)
            self.execution_analyzer.report_failure(execution_id, str(e))